package com.v246.common;

import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.io.inputstream.ZipInputStream;
import net.lingala.zip4j.model.LocalFileHeader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.apache.pdfbox.text.PDFTextStripperByArea;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.awt.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/01/08
 */
public class InvoiceKit {

    public static void main(String args[]) throws Exception {
        List<String> fileList = new ArrayList<String>();
        String path = "C:\\selenium\\txff";
        File f = new File(path);
        if (!f.exists()) {
            System.out.println(path + " not exists");
        }
        File files[] = f.listFiles();
        for (File file : files) {
            System.out.println(file.getName());
            extractWithZipInputStream(file, "destFolder");
        }
        File pdfs = new File("C:\\selenium\\txff_pdf");
        File[] pdfFiles = pdfs.listFiles();
        InvoiceKit pdfKit = new InvoiceKit();
        List<Map<String, String>> excelList = new ArrayList<>();
        for (File pdfFile : pdfFiles) {
            HashMap<String, String> invoiceDetail = pdfKit.readPdf(pdfFile);
            invoiceDetail.put("文件名", pdfFile.getName());
            excelList.add(invoiceDetail);
        }
        pdfKit.writeExcel(excelList, "C:\\selenium\\txff_excel\\fp_temp.xlsx");
    }

    public static List<Map<String, String>> readInvoice(String destFolder) throws Exception {
        File destFiles = new File(destFolder);
        File files[] = destFiles.listFiles();
        String pdfFolder = destFolder + File.separator + "pdfs";
        Files.createDirectories(Paths.get(pdfFolder));
        for (File file : files) {
            if (!file.isDirectory() && checkZip(file.getName())) {
                extractWithZipInputStream(file, pdfFolder);
            }
        }
        File pdfs = new File(pdfFolder);
        InvoiceKit pdfKit = new InvoiceKit();
        List<Map<String, String>> excelList = new ArrayList<>();
        for (File pdfFile : pdfs.listFiles()) {
            System.out.println(pdfFile.getName());
            HashMap<String, String> invoiceDetail = pdfKit.readPdf(pdfFile);
            invoiceDetail.put("文件名", pdfFile.getName());
            invoiceDetail.put("path", pdfFile.getPath());
            excelList.add(invoiceDetail);
        }
        return excelList;
    }

    public static boolean checkZip(String fileName) {
        //“.”和“|”都是转义字符,必须得加"\\"
        String[] str = fileName.split("\\.");
        String suffix = str[1];
        if ("zip".equalsIgnoreCase(suffix)) {
            return true;
        } else {
            return false;
        }
    }

    public static void deleteIfExists(File file) throws IOException {
        if (file.exists()) {
            if (file.isFile()) {
                if (!file.delete()) {
                    throw new IOException("Delete file failure,path:" + file.getAbsolutePath());
                }
            } else {
                File[] files = file.listFiles();
                if (files != null && files.length > 0) {
                    for (File temp : files) {
                        deleteIfExists(temp);
                    }
                }
                if (!file.delete()) {
                    throw new IOException("Delete file failure,path:" + file.getAbsolutePath());
                }
            }
        }
    }

    /**
     * 删除文件或文件夹
     */
    public static void deleteIfExists(String path) throws IOException {
        deleteIfExists(new File(path));
    }

    public static void extractWithZipInputStream(File zipFile, String destFolder) throws IOException {
        LocalFileHeader localFileHeader;
        int readLen;
        byte[] readBuffer = new byte[4096];
        InputStream inputStream = new FileInputStream(zipFile);
        try (ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {
            while ((localFileHeader = zipInputStream.getNextEntry()) != null) {
                if ("invoice.zip".equalsIgnoreCase(localFileHeader.getFileName())) {
                    deleteIfExists("invoice.zip");
                    File extractedFile = new File(localFileHeader.getFileName());
                    try (OutputStream outputStream = new FileOutputStream(extractedFile)) {
                        while ((readLen = zipInputStream.read(readBuffer)) != -1) {
                            outputStream.write(readBuffer, 0, readLen);
                        }
                    }
                    new ZipFile(extractedFile).extractAll(destFolder);
                }

            }
        }
        deleteIfExists("invoice.zip");
    }

    public static void writeExcel(List<Map<String, String>> mapList, String destFilePath) throws
            IOException {
        Workbook wb = null;
        File excelTempFile = getTempFilePath();
        FileInputStream fileInput = new FileInputStream(excelTempFile);
        if ("xls".equals(excelTempFile.getName().substring(excelTempFile.getName().lastIndexOf(".") + 1))) {
            wb = new HSSFWorkbook(fileInput);
        } else {
            wb = new XSSFWorkbook(fileInput);
        }
        Sheet sheet = wb.getSheetAt(0);
        Row head = sheet.getRow(0);
        for (Map<String, String> map : mapList) {
            Row newRow = sheet.createRow(sheet.getLastRowNum() + 1);
            for (int i = 0; i < head.getPhysicalNumberOfCells(); i++) {
                Cell newCell = newRow.createCell(i);
                String key = head.getCell(i).toString();
                newCell.setCellValue(map.get(key));
            }
        }
        FileOutputStream fileOutputStream = new FileOutputStream(destFilePath);
        wb.write(fileOutputStream);
        fileOutputStream.close();
    }

    public static File getTempFilePath() {
        InvoiceKit kit = new InvoiceKit();
        String path = kit.getClass().getResource("/temp.xlsx").getPath();
        System.out.println(path);
        File f = new File(path);
        return f;
    }

    public HashMap<String, String> readPdf(File file) throws Exception {
        //设置识别窗口大小
        Rectangle header = new Rectangle(10, 7, 762, 80);
        Rectangle purchaser = new Rectangle(103, 90, 220, 60);
        Rectangle password = new Rectangle(350, 80, 306, 70);
        Rectangle tax = new Rectangle(10, 160, 762, 40);
        Rectangle sum = new Rectangle(120, 250, 700, 50);
        Rectangle seller = new Rectangle(103, 300, 220, 60);
        Rectangle remark = new Rectangle(360, 300, 300, 60);
        Rectangle end = new Rectangle(10, 360, 400, 38);
        String passwordString = "";
        String headerString = "";
        String purchaserString = "";
        String taxString = "";
        String sumString = "";
        String sellerString = "";
        String remarkString = "";
        String endString = "";
        HashMap<String, String> map = new HashMap<>(30);

        // 根据矩形窗口读PDF
        PDDocument doc = PDDocument.load(file);
        PDFTextStripperByArea stripper = new PDFTextStripperByArea();
        // 将窗口添加入stripper
        stripper.addRegion("header", header);
        stripper.addRegion("purchaser", purchaser);
        stripper.addRegion("password", password);
        stripper.addRegion("tax", tax);
        stripper.addRegion("sum", sum);
        stripper.addRegion("seller", seller);
        stripper.addRegion("remark", remark);
        stripper.addRegion("end", end);
        PDPageTree allPages = doc.getDocumentCatalog().getPages();
        PDPage firstPage = (PDPage) allPages.get(0);
        stripper.extractRegions(firstPage);
        // 根据识别窗口读取数据
        passwordString = stripper.getTextForRegion("password");
        headerString = stripper.getTextForRegion("header");
        purchaserString = stripper.getTextForRegion("purchaser");
        taxString = stripper.getTextForRegion("tax");
        sumString = stripper.getTextForRegion("sum");
        sellerString = stripper.getTextForRegion("seller");
        remarkString = stripper.getTextForRegion("remark");
        endString = stripper.getTextForRegion("end");
        // 读取完成，关闭文档
        doc.close();

        // 分割字符串，将数据存入HashMap
        passwordString = passwordString.replace("\r\n", "").replace(" ", "");
        remarkString = remarkString.replace("\n", "").replace("\r", "");
        String[] endArray = endString.replace("\r\n", " ").split("[: " + " ]");
        String[] sellerArray = sellerString.replace("\r\n", " ").split("[: " + " ]");
        String[] taxArray = taxString.replace("\r\n", " ").split("[: " + " ]");
        String[] purchaserArray = purchaserString.replace("\r\n", " ").split("[: " + " ]");
        String[] headerArray = headerString.replace("\r\n", " ").split("[: " + " ]");
        String[] sumArray = sumString.replace("\r\n", " ").replace("￥", "").split("[:" + "￥" + " ]");

        if (sumArray.length >= 5 && sellerArray.length >= 4 && taxArray.length >= 7 && headerArray.length >= 20
                && purchaserArray.length >= 2 && endArray.length >= 13 && passwordString != "" && remarkString != "") {
            map.put("密码区", passwordString);
            map.put("价税合计（大写）", sumArray[3]);
            map.put("税价合计", sumArray[4]);
            map.put("备注", remarkString);
            map.put("收款人", endArray[10]);
            map.put("复核", endArray[11]);
            map.put("开票人", endArray[12]);
            map.put("开票抬头", sellerArray[0]);
            map.put("销售方纳税人识别号", sellerArray[1]);
            map.put("销售方地址、电话", sellerArray[2]);
            map.put("销售方开户行及账号", sellerArray[3]);
            map.put("项目名称", taxArray[0]);
            map.put("车牌号", taxArray[1]);
            map.put("类型", taxArray[2]);
            map.put("通行日期起", taxArray[3]);
            map.put("通行日期止", taxArray[4]);
            map.put("金额", sumArray[1]);
            map.put("税率", taxArray[6]);
            map.put("税额", sumArray[2]);
            map.put("购买方名称", purchaserArray[0]);
            map.put("购买方纳税人识别号", purchaserArray[1]);
            map.put("购买方地址、电话", purchaserArray.length >= 3 ? purchaserArray[2] : "");
            map.put("购买方开户行及账号", purchaserArray.length >= 4 ? purchaserArray[3] : "");
            map.put("发票类型", headerArray[0]);
            map.put("机器编号", headerArray[12]);
            map.put("发票代码", headerArray[13]);
            map.put("发票号码", headerArray[14]);
            map.put("开票日期", headerArray[15]);
            map.put("校验码", headerArray[16] + headerArray[17] + headerArray[18] + headerArray[19]);
        } else {
            throw new Exception();
        }
        return map;
    }

    public HashMap<String, String> readPdf(String readPath) throws Exception {
        return this.readPdf(new File(readPath));
    }
}
