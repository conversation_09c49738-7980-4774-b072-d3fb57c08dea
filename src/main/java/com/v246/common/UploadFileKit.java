package com.v246.common;


import com.v246.base.BaseController;
import com.v246.common.kit.MongoKit;
import com.v246.common.kit.MyBatisKit;
import com.v246.common.mongodb.MongoFile;
import com.v246.common.vo.UploadFile;
import com.v246.pc.tabledata.rpauploadfile.service.IRpaUploadFileService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

@Tag(name = "上传附件/图片", description = "上传附件/图片")
@RestController
@RequestMapping("/rpaUploadFile")
@Component
@Slf4j
public class UploadFileKit extends BaseController {

    @Autowired
    IRpaUploadFileService rpaUploadFileService;
    @Autowired
    MyBatisKit myBatisKit;

    @Autowired
    private MongoKit mongoKit;

    @Operation(summary = "文件上传到mongodb", description = "文件上传到mongodb")
    @PostMapping(value = "/uploadFile")
    public Map uploadFile(@RequestParam(value = "file") MultipartFile file) throws Exception {
        String originalFilename = file.getOriginalFilename();
        String suffixName = originalFilename.substring(originalFilename.lastIndexOf("."));
        MongoFile mf = new MongoFile();
        mf.setFile(file.getInputStream());
        mf.setContentType(file.getContentType());
        mf.setCreateUser("1");
        mf.setOriginalName(originalFilename);
        mf.setExtName(suffixName);
        mf.setContentType("application/pdf");
        UploadFile mongoFile = mongoKit.upload(mf);
        log.info("save after 文件上传成功，文件名：{}", mongoFile.getOriginalName());
        log.info("save after 文件上传成功，pk：{}", mongoFile.getPk());
        log.info("save after 文件上传成功，contentType：{}", mongoFile.getContentType());
        log.info("save after 文件上传成功，extName：{}", mongoFile.getExtName());
        log.info("save after 文件上传成功，size：{}", mongoFile.getFile().available());

        UploadFile m = mongoKit.findByFileId(mongoFile.getPk());
        if(m == null){
            throw new Exception("文件上传失败");
        }
        log.info("get it back 文件上传成功，文件名：{}", m.getOriginalName());
        log.info("get it back 文件上传成功，pk：{}", m.getPk());
        log.info("get it back 文件上传成功，contentType：{}", m.getContentType());
        log.info("get it back 文件上传成功，extName：{}", m.getExtName());
        log.info("get it back 文件上传成功，size：{}", m.getFile().available());

        Map<String, String> jo = new HashMap<>(16);
        jo.put("filePk", mongoFile.getPk());
        jo.put("fileName", originalFilename);
        jo.put("fileExam", suffixName.replace(".", ""));
        jo.put("filePath", "http://localhost:8080/pub-api/pc/fileDownload/"+mongoFile.getPk());
        return jo;
    }


}
