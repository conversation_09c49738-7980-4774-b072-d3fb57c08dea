package com.v246.pc.tabledata.rpainvoice.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigInteger;
import java.math.BigDecimal;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotBlank;
/**
 * <AUTHOR> generate engine
 * @create 2021-01-13 10:33:28
 **/
@Data
public class RpaInvoiceUpdateDTO {
    private static final long serialVersionUID = 1L;
    private Long pk;
    //发票类型
    @Schema(description = " 发票类型 ")
    private String invoiceType;
    //机器编号
    @Schema(description = " 机器编号 ")
    private String machineNo;
    //发票代码
    @Schema(description = " 发票代码 ")
    private String invoiceCode;
    //发票号码
    @Schema(description = " 发票号码 ")
    private String invoiceNo;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //开票日期
    @Schema(description = " 开票日期 ")
    private Timestamp invoiceOpenDate;
    //校验码
    @Schema(description = " 校验码 ")
    private String invoiceCheckNo;
    //购买方名称
    @Schema(description = " 购买方名称 ")
    private String purchaserName;
    //购买方纳税人识别号
    @Schema(description = " 购买方纳税人识别号 ")
    private String purchaserCode;
    //购买方地址、电话
    @Schema(description = " 购买方地址、电话 ")
    private String purchaserAddressPhone;
    //购买方开户行及账号
    @Schema(description = " 购买方开户行及账号 ")
    private String purchaserBank;
    //密码区
    @Schema(description = " 密码区 ")
    private String passwordArea;
    //项目名称
    @Schema(description = " 项目名称 ")
    private String projectName;
    //车牌号
    @Schema(description = " 车牌号 ")
    private String vehicleName;
    //类型
    @Schema(description = " 类型 ")
    private String type;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //通行日期起
    @Schema(description = " 通行日期起 ")
    private Timestamp passageBeginDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //通行日期止
    @Schema(description = " 通行日期止 ")
    private Timestamp passageEndDate;
    //金额
    @Schema(description = " 金额 ")
    private BigDecimal invoiceMoney;
    //税率
    @Schema(description = " 税率 ")
    private String invoiceRate;
    //税额
    @Schema(description = " 税额 ")
    private BigDecimal invoiceTax;
    //价税合计（大写）
    @Schema(description = " 价税合计（大写） ")
    private String invoiceTotalPrice;
    //税价合计
    @Schema(description = " 税价合计 ")
    private BigDecimal invoicePrice;
    //开票抬头
    @Schema(description = " 开票抬头 ")
    private String invoiceHead;
    //销售方纳税人识别号
    @Schema(description = " 销售方纳税人识别号 ")
    private String sellerCode;
    //销售方地址、电话
    @Schema(description = " 销售方地址、电话 ")
    private String sellerAddressPhone;
    //销售方开户行及账号
    @Schema(description = " 销售方开户行及账号 ")
    private String sellerBank;
    //备注
    @Schema(description = " 备注 ")
    private String invoiceNote;
    //收款人
    @Schema(description = " 收款人 ")
    private String payee;
    //复核
    @Schema(description = " 复核 ")
    private String review;
    //开票人
    @Schema(description = " 开票人 ")
    private String drawer;
    //文件名
    @Schema(description = " 文件名 ")
    private String fileName;
    //停用系统来源描述
    @Schema(description = " 停用系统来源描述 ")
    private String deleteSource;
}
