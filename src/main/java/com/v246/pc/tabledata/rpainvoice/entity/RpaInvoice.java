package com.v246.pc.tabledata.rpainvoice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.v246.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigInteger;
import java.math.BigDecimal;
import com.v246.pc.common.entity.DefaultEntity;
import com.baomidou.mybatisplus.annotation.KeySequence;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> generate engine
 * @since 2021-01-13 10:33:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(name="", description="")
public class RpaInvoice extends DefaultEntity {

    private static final long serialVersionUID = 1L;
     //主键
     @Schema(description = " 主键 ")
             @TableId(value = "pk",type = IdType.AUTO)
     @TableField("pk")
    private Long pk;
     //发票类型
     @Schema(description = " 发票类型 ")
     @TableField("invoice_type")
    private String invoiceType;
     //机器编号
     @Schema(description = " 机器编号 ")
     @TableField("machine_no")
    private String machineNo;
     //发票代码
     @Schema(description = " 发票代码 ")
     @TableField("invoice_code")
    private String invoiceCode;
     //发票号码
     @Schema(description = " 发票号码 ")
     @TableField("invoice_no")
    private String invoiceNo;
     //开票日期
     @Schema(description = " 开票日期 ")
     @TableField("invoice_open_date")
    private Timestamp invoiceOpenDate;
     //校验码
     @Schema(description = " 校验码 ")
     @TableField("invoice_check_no")
    private String invoiceCheckNo;
     //购买方名称
     @Schema(description = " 购买方名称 ")
     @TableField("purchaser_name")
    private String purchaserName;
     //购买方纳税人识别号
     @Schema(description = " 购买方纳税人识别号 ")
     @TableField("purchaser_code")
    private String purchaserCode;
     //购买方地址、电话
     @Schema(description = " 购买方地址、电话 ")
     @TableField("purchaser_address_phone")
    private String purchaserAddressPhone;
     //购买方开户行及账号
     @Schema(description = " 购买方开户行及账号 ")
     @TableField("purchaser_bank")
    private String purchaserBank;
     //密码区
     @Schema(description = " 密码区 ")
     @TableField("password_area")
    private String passwordArea;
     //项目名称
     @Schema(description = " 项目名称 ")
     @TableField("project_name")
    private String projectName;
     //车牌号
     @Schema(description = " 车牌号 ")
     @TableField("vehicle_name")
    private String vehicleName;
     //类型
     @Schema(description = " 类型 ")
     @TableField("type")
    private String type;
     //通行日期起
     @Schema(description = " 通行日期起 ")
     @TableField("passage_begin_date")
    private Timestamp passageBeginDate;
     //通行日期止
     @Schema(description = " 通行日期止 ")
     @TableField("passage_end_date")
    private Timestamp passageEndDate;
     //金额
     @Schema(description = " 金额 ")
     @TableField("invoice_money")
    private BigDecimal invoiceMoney;
     //税率
     @Schema(description = " 税率 ")
     @TableField("invoice_rate")
    private String invoiceRate;
     //税额
     @Schema(description = " 税额 ")
     @TableField("invoice_tax")
    private BigDecimal invoiceTax;
     //价税合计（大写）
     @Schema(description = " 价税合计（大写） ")
     @TableField("invoice_total_price")
    private String invoiceTotalPrice;
     //税价合计
     @Schema(description = " 税价合计 ")
     @TableField("invoice_price")
    private BigDecimal invoicePrice;
     //开票抬头
     @Schema(description = " 开票抬头 ")
     @TableField("invoice_head")
    private String invoiceHead;
     //销售方纳税人识别号
     @Schema(description = " 销售方纳税人识别号 ")
     @TableField("seller_code")
    private String sellerCode;
     //销售方地址、电话
     @Schema(description = " 销售方地址、电话 ")
     @TableField("seller_address_phone")
    private String sellerAddressPhone;
     //销售方开户行及账号
     @Schema(description = " 销售方开户行及账号 ")
     @TableField("seller_bank")
    private String sellerBank;
     //备注
     @Schema(description = " 备注 ")
     @TableField("invoice_note")
    private String invoiceNote;
     //收款人
     @Schema(description = " 收款人 ")
     @TableField("payee")
    private String payee;
     //复核
     @Schema(description = " 复核 ")
     @TableField("review")
    private String review;
     //开票人
     @Schema(description = " 开票人 ")
     @TableField("drawer")
    private String drawer;
     //文件名
     @Schema(description = " 文件名 ")
     @TableField("file_name")
    private String fileName;
     //停用系统来源描述
     @Schema(description = " 停用系统来源描述 ")
     @TableField("delete_source")
    private String deleteSource;

    //账号
    @Schema(description = " 账号 ")
    @TableField("account_name")
    private String accountName;
    //密码
    @Schema(description = " 密码 ")
    @TableField("account_password")
    private String accountPassword;
    //停用系统来源描述

}
