package com.v246.pc.tabledata.rpainvoice.controller;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.v246.common.config.annotation.SkipInterceptor;
import com.v246.common.kit.MyBatisKit;
import com.v246.spring.base.core.Result;
import com.v246.spring.base.core.database.Record;
import com.v246.pc.tabledata.rpainvoice.service.IRpaInvoiceService;
import com.v246.pc.tabledata.rpainvoice.dto.request.RpaInvoiceInsertDTO;
import com.v246.pc.tabledata.rpainvoice.dto.request.RpaInvoiceUpdateDTO;
import com.v246.pc.tabledata.rpainvoice.dto.response.RpaInvoiceListDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.v246.base.BaseController;
import java.math.BigInteger;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> generate engine
 * @since 2021-01-13 10:33:28
 */
@Tag(name = "", description = "")
@RestController
@RequestMapping("/rpaInvoice")
public class RpaInvoiceController extends BaseController {
    @Autowired
    IRpaInvoiceService rpaInvoiceService;
    @Autowired
    MyBatisKit myBatisKit;

    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping(value = "/queryPage")
    public Result queryPage(
            @Parameter(description = "{\"filters\": [],\"paginate\":{\"pageNumber\": 1,\"pageSize\": 50}}")
            @RequestBody JSONObject json
    ) {
        try {
            Page<Record> record = rpaInvoiceService.queryPage(myBatisKit.getPageByFilterJson(json));
            return Result.success(record);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }
    @Operation(summary = "列表", description = "列表")
    @PostMapping(value = "/queryList")
    public Result queryList(
    ) {
        try {
            List<RpaInvoiceListDTO> list = rpaInvoiceService.queryList();
            return Result.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }

    @Operation(summary = "新增", description = "新增")
    @PostMapping(value = "/add")
    public Result add(@RequestBody RpaInvoiceInsertDTO insertDTO) {
    rpaInvoiceService.save(insertDTO);
        return Result.success();
    }

    @Operation(summary = "修改", description = "修改")
    @PostMapping(value = "/update")
    public Result update(@RequestBody RpaInvoiceUpdateDTO updateDTO) {
    rpaInvoiceService.update(updateDTO);
        return Result.success();
    }

    @Operation(summary = "物理删除", description = "物理删除")
    @PostMapping(value = "/delete")
    public Result delete(Long pk) {
        rpaInvoiceService.delete(pk);
        return Result.success();
    }

    @Operation(summary = "逻辑删除", description = "逻辑删除")
    @PostMapping(value = "/deleteLogic")
    public Result deleteLogic(Long pk) {
        rpaInvoiceService.deleteLogic(pk);
        return Result.success();
    }

    @Operation(summary = "物理批量删除", description = "物理批量删除")
    @PostMapping(value = "/deleteByIds")
    public Result deleteByIds(@RequestBody Long[] idList) {
        rpaInvoiceService.deleteByIds(Arrays.asList(idList));
        return Result.success();
    }

    @Operation(summary = "逻辑批量删除", description = "逻辑批量删除")
    @PostMapping(value = "/deleteLogicByIds")
    public Result deleteLogicByIds(@RequestBody Long[] idList) {
        rpaInvoiceService.deleteLogicByIds(Arrays.asList(idList));
        return Result.success();
    }

    @Operation(summary = "批量更新", description = "批量更新")
    @PostMapping(value = "/updateByList")
    public Result updateByList(@RequestBody RpaInvoiceUpdateDTO[] rpaInvoiceUpdateDTOList) {
        rpaInvoiceService.updateByList(Arrays.asList(rpaInvoiceUpdateDTOList));
        return Result.success();
    }


    @Operation(summary = "按照其他字段更新", description = "此方法仅供参考，不能直接使用")
    @PostMapping(value = "/updateByOther")
    public Result updateByOther(@RequestBody RpaInvoiceUpdateDTO updateDTO) {
        rpaInvoiceService.updateByOther(updateDTO);
        return Result.success();
    }

    @Operation(summary = "按照其他字段删除", description = "此方法仅供参考，不能直接使用")
    @PostMapping(value = "/deleteByOther")
    public Result deleteByOther(@RequestBody RpaInvoiceUpdateDTO updateDTO) {
        rpaInvoiceService.deleteByOther(updateDTO);
        return Result.success();
    }

}
