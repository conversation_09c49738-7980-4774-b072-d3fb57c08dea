package com.v246.pc.tabledata.rpatransactionrecord.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigInteger;
import java.math.BigDecimal;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> generate engine
 * @create 2022-06-07 01:32:16
 **/
@Data
public class RpaTransactionRecordInsertDTO {
    private static final long serialVersionUID = 1L;
    //车牌号
    @Schema(description = " 车牌号 ")
    private String vehicleName;
    //获取月份
    @Schema(description = " 获取月份 ")
    private String gainMonth;
    //获取月份标识
    @Schema(description = " 获取月份标识 ")
    private String gainMonthFlag;
    //状态(WAIT 待完成 ；SUCCESS:成功  FAIL：失败)
    @Schema(description = " 状态(WAIT 待完成 ；SUCCESS:成功  FAIL：失败) ")
    private String status;
    //批号
    @Schema(description = " 批号 ")
    private String batchNumber;
    //交易数量
    @Schema(description = " 交易数量 ")
    private Long transactionNum;
    //交易金额
    @Schema(description = " 交易金额 ")
    private BigDecimal transactionMoney;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //完成时间
    @Schema(description = " 完成时间 ")
    private Timestamp finishDate;
    //账号
    @Schema(description = " 账号 ")
    private String accountName;
    //密码
    @Schema(description = " 密码 ")
    private String accountPassword;
    //停用系统来源描述
    @Schema(description = " 停用系统来源描述 ")
    private String deleteSource;
}
