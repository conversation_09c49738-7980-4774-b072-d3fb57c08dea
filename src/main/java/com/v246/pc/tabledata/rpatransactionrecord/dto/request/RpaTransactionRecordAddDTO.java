package com.v246.pc.tabledata.rpatransactionrecord.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.v246.pc.tabledata.rpavehicle.dto.request.RpaVehicleAddDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> generate engine
 * @create 2022-06-07 01:32:16
 **/
@Data
public class RpaTransactionRecordAddDTO {
    private static final long serialVersionUID = 1L;

    //车牌号
    @Schema(description = " 车牌号 ")
    private List<RpaVehicleAddDTO> vehicleNameList;
    //获取月份
    @Schema(description = " 获取月份 ")
    @TableField("gain_month")
    private String gainMonth;


}
