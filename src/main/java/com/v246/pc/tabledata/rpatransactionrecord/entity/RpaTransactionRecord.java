package com.v246.pc.tabledata.rpatransactionrecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.v246.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigInteger;
import java.math.BigDecimal;
import com.v246.pc.common.entity.DefaultEntity;
import com.baomidou.mybatisplus.annotation.KeySequence;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> generate engine
 * @since 2022-06-07 01:32:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(name="", description="")
public class RpaTransactionRecord extends DefaultEntity {

    private static final long serialVersionUID = 1L;
     //主键
     @Schema(description = " 主键 ")
             @TableId(value = "pk",type = IdType.AUTO)
     @TableField("pk")
    private Long pk;
     //车牌号
     @Schema(description = " 车牌号 ")
     @TableField("vehicle_name")
    private String vehicleName;
     //获取月份
     @Schema(description = " 获取月份 ")
     @TableField("gain_month")
    private String gainMonth;
     //获取月份标识
     @Schema(description = " 获取月份标识 ")
     @TableField("gain_month_flag")
    private String gainMonthFlag;
     //状态(WAIT 待完成 ；SUCCESS:成功  FAIL：失败)
     @Schema(description = " 状态(WAIT 待完成 ；SUCCESS:成功  FAIL：失败) ")
     @TableField("status")
    private String status;
     //批号
     @Schema(description = " 批号 ")
     @TableField("batch_number")
    private String batchNumber;
     //交易数量
     @Schema(description = " 交易数量 ")
     @TableField("transaction_num")
    private Long transactionNum;
     //交易金额
     @Schema(description = " 交易金额 ")
     @TableField("transaction_money")
    private BigDecimal transactionMoney;
     //完成时间
     @Schema(description = " 完成时间 ")
     @TableField("finish_date")
    private Timestamp finishDate;
     //账号
     @Schema(description = " 账号 ")
     @TableField("account_name")
    private String accountName;
     //密码
     @Schema(description = " 密码 ")
     @TableField("account_password")
    private String accountPassword;
     //停用系统来源描述
     @Schema(description = " 停用系统来源描述 ")
     @TableField("delete_source")
    private String deleteSource;



}
