package com.v246.pc.tabledata.rpavehicle.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.v246.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigInteger;
import java.math.BigDecimal;
import com.v246.pc.common.entity.DefaultEntity;
import com.baomidou.mybatisplus.annotation.KeySequence;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> generate engine
 * @since 2021-01-13 10:33:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(name="", description="")
public class RpaVehicle extends DefaultEntity {

    private static final long serialVersionUID = 1L;
     //主键
     @Schema(description = " 主键 ")
             @TableId(value = "pk",type = IdType.AUTO)
     @TableField("pk")
    private Long pk;
     //车牌号
     @Schema(description = " 车牌号 ")
     @TableField("vehicle_name")
    private String vehicleName;
     //司机姓名
     @Schema(description = " 司机姓名 ")
     @TableField("driver_name")
    private String driverName;
     //司机身份证号
     @Schema(description = " 司机身份证号 ")
     @TableField("driver_card")
    private String driverCard;
     //停用系统来源描述
     @Schema(description = " 停用系统来源描述 ")
     @TableField("delete_source")
    private String deleteSource;
    //账号
    @Schema(description = " 账号 ")
    @TableField("account_name")
    private String accountName;
    //密码
    @Schema(description = " 密码 ")
    @TableField("account_password")
    private String accountPassword;


}
