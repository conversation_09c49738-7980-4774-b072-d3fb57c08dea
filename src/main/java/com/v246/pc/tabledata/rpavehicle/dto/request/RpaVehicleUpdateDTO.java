package com.v246.pc.tabledata.rpavehicle.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigInteger;
import java.math.BigDecimal;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotBlank;
/**
 * <AUTHOR> generate engine
 * @create 2021-01-13 10:33:28
 **/
@Data
public class RpaVehicleUpdateDTO {
    private static final long serialVersionUID = 1L;
    private Long pk;
    //司机姓名
    @Schema(description = " 司机姓名 ")
    private String driverName;
    //司机身份证号
    @Schema(description = " 司机身份证号 ")
    private String driverCard;
}
