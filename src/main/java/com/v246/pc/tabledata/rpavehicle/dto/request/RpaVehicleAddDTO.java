package com.v246.pc.tabledata.rpavehicle.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RpaVehicleAddDTO {

    //账号
    @Schema(description = " 账号 ")
    @TableField("account_name")
    private String accountName;
    //密码
    @Schema(description = " 密码 ")
    @TableField("account_password")
    private String accountPassword;

    //车牌号
    @Schema(description = " 车牌号 ")
    @TableField("vehicle_name")
    private String vehicleName;
}
