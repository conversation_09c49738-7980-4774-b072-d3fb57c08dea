package com.v246.pc.tabledata.rpastubrecord.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigInteger;
import java.math.BigDecimal;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> generate engine
 * @create 2021-01-13 10:33:28
 **/
@Data
public class RpaStubRecordInsertDTO {
    private static final long serialVersionUID = 1L;
    //车牌号
    @Schema(description = " 车牌号 ")
    private String vehicleName;
    //状态
    @Schema(description = " 状态 ")
    private String status;
    //批号
    @Schema(description = " 批号 ")
    private String batchNumber;
    //发票张数
    @Schema(description = " 发票张数 ")
    private Long invoiceNum;
    //完成时间
    @Schema(description = " 完成时间 ")
    private String finishDate;
    //停用系统来源描述
    @Schema(description = " 停用系统来源描述 ")
    private String deleteSource;
}
