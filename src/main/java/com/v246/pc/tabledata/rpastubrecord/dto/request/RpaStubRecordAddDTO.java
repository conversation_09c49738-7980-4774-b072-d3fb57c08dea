package com.v246.pc.tabledata.rpastubrecord.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.v246.pc.tabledata.rpavehicle.dto.request.RpaVehicleAddDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class RpaStubRecordAddDTO {

    private static final long serialVersionUID = 1L;
    //车牌号
    @Schema(description = " 车牌号 ")
    private List<RpaVehicleAddDTO> vehicleNameList;
    //获取月份
    @Schema(description = " 获取月份 ")
    @TableField("gain_month")
    private String gainMonth;
}
