package com.v246.pc.tabledata.rpastubrecord.dto.response;

import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.v246.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigInteger;
import java.math.BigDecimal;
import com.v246.pc.common.entity.DefaultEntity;
/**
* <p>
    *
    * </p>
*
* <AUTHOR> generate engine
* @since 2021-01-13 10:33:28
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(name="", description="")
public class RpaStubRecordListDTO extends DefaultEntity {

private static final long serialVersionUID = 1L;
        //主键
        @Schema(description = " 主键 ")
            @TableId(value = "pk",type = IdType.AUTO)
        @TableField("pk")
        private Long pk;
        //车牌号
        @Schema(description = " 车牌号 ")
        @TableField("vehicle_name")
        private String vehicleName;
        //状态
        @Schema(description = " 状态 ")
        @TableField("status")
        private String status;
        //批号
        @Schema(description = " 批号 ")
        @TableField("batch_number")
        private String batchNumber;
        //发票张数
        @Schema(description = " 发票张数 ")
        @TableField("invoice_num")
        private String invoiceNum;
        //完成时间
        @Schema(description = " 完成时间 ")
        @TableField("finish_date")
        private String finishDate;
        //停用系统来源描述
        @Schema(description = " 停用系统来源描述 ")
        @TableField("delete_source")
        private String deleteSource;



}
