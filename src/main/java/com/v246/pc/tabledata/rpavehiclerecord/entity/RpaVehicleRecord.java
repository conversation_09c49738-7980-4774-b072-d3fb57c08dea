package com.v246.pc.tabledata.rpavehiclerecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.v246.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigInteger;
import java.math.BigDecimal;
import com.v246.pc.common.entity.DefaultEntity;
import com.baomidou.mybatisplus.annotation.KeySequence;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> generate engine
 * @since 2021-01-25 08:36:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(name="", description="")
public class RpaVehicleRecord extends DefaultEntity {

    private static final long serialVersionUID = 1L;
     //主键
     @Schema(description = " 主键 ")
             @TableId(value = "pk",type = IdType.AUTO)
     @TableField("pk")
    private Long pk;
     //账号
     @Schema(description = " 账号 ")
     @TableField("account_name")
    private String accountName;
     //密码
     @Schema(description = " 密码 ")
     @TableField("account_password")
    private String accountPassword;
     //状态(WAIT 待完成 ；SUCCESS:成功  FAIL：失败)
     @Schema(description = " 状态(WAIT 待完成 ；SUCCESS:成功  FAIL：失败) ")
     @TableField("status")
    private String status;
     //车辆数
     @Schema(description = " 车辆数 ")
     @TableField("vehicle_num")
    private Long vehicleNum;
     //完成时间
     @Schema(description = " 完成时间 ")
     @TableField("finish_date")
    private Timestamp finishDate;
     //停用系统来源描述
     @Schema(description = " 停用系统来源描述 ")
     @TableField("delete_source")
    private String deleteSource;



}
