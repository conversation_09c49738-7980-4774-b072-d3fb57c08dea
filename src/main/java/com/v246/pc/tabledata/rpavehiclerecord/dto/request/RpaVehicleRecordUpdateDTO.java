package com.v246.pc.tabledata.rpavehiclerecord.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigInteger;
import java.math.BigDecimal;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotBlank;
/**
 * <AUTHOR> generate engine
 * @create 2021-01-25 08:36:50
 **/
@Data
public class RpaVehicleRecordUpdateDTO {
    private static final long serialVersionUID = 1L;
    private Long pk;
    //账号
    @Schema(description = " 账号 ")
    private String accountName;
    //密码
    @Schema(description = " 密码 ")
    private String accountPassword;
    //状态(WAIT 待完成 ；SUCCESS:成功  FAIL：失败)
    @Schema(description = " 状态(WAIT 待完成 ；SUCCESS:成功  FAIL：失败) ")
    private String status;
    //车辆数
    @Schema(description = " 车辆数 ")
    private Long vehicleNum;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //完成时间
    @Schema(description = " 完成时间 ")
    private Timestamp finishDate;
    //停用系统来源描述
    @Schema(description = " 停用系统来源描述 ")
    private String deleteSource;
}
