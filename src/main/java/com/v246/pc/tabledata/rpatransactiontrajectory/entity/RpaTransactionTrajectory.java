package com.v246.pc.tabledata.rpatransactiontrajectory.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.v246.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigInteger;
import java.math.BigDecimal;
import com.v246.pc.common.entity.DefaultEntity;
import com.baomidou.mybatisplus.annotation.KeySequence;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> generate engine
 * @since 2022-06-07 01:32:15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(name="", description="")
public class RpaTransactionTrajectory extends DefaultEntity {

    private static final long serialVersionUID = 1L;
     //主键
     @Schema(description = " 主键 ")
             @TableId(value = "pk",type = IdType.AUTO)
     @TableField("pk")
    private Long pk;
     //车牌号
     @Schema(description = " 车牌号 ")
     @TableField("vehicle_name")
    private String vehicleName;
     //账号
     @Schema(description = " 账号 ")
     @TableField("account_name")
    private String accountName;
     //密码
     @Schema(description = " 密码 ")
     @TableField("account_password")
    private String accountPassword;
     //入口省简称
     @Schema(description = " 入口省简称 ")
     @TableField("entrance_province_attr_name")
    private String entranceProvinceAttrName;
     //出口省简称
     @Schema(description = " 出口省简称 ")
     @TableField("exit_province_attr_name")
    private String exitProvinceAttrName;
     //入口
     @Schema(description = " 入口 ")
     @TableField("entrance")
    private String entrance;
     //出口
     @Schema(description = " 出口 ")
     @TableField("exitus")
    private String exitus;
     //交易时间
     @Schema(description = " 交易时间 ")
     @TableField("trading_date")
    private Timestamp tradingDate;
     //交易金额
     @Schema(description = " 交易金额 ")
     @TableField("trading_money")
    private BigDecimal tradingMoney;
     //消费类型
     @Schema(description = " 消费类型 ")
     @TableField("trading_type")
    private String tradingType;
     //开票情况
     @Schema(description = " 开票情况 ")
     @TableField("trading_invoice_status")
    private String tradingInvoiceStatus;
     //停用系统来源描述
     @Schema(description = " 停用系统来源描述 ")
     @TableField("delete_source")
    private String deleteSource;



}
