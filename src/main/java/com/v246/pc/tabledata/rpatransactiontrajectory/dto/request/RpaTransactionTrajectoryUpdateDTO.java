package com.v246.pc.tabledata.rpatransactiontrajectory.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigInteger;
import java.math.BigDecimal;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotBlank;
/**
 * <AUTHOR> generate engine
 * @create 2022-06-07 01:32:15
 **/
@Data
public class RpaTransactionTrajectoryUpdateDTO {
    private static final long serialVersionUID = 1L;
    private Long pk;
    //车牌号
    @Schema(description = " 车牌号 ")
    private String vehicleName;
    //账号
    @Schema(description = " 账号 ")
    private String accountName;
    //密码
    @Schema(description = " 密码 ")
    private String accountPassword;
    //入口省简称
    @Schema(description = " 入口省简称 ")
    private String entranceProvinceAttrName;
    //出口省简称
    @Schema(description = " 出口省简称 ")
    private String exitProvinceAttrName;
    //入口
    @Schema(description = " 入口 ")
    private String entrance;
    //出口
    @Schema(description = " 出口 ")
    private String exitus;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //交易时间
    @Schema(description = " 交易时间 ")
    private Timestamp tradingDate;
    //交易金额
    @Schema(description = " 交易金额 ")
    private BigDecimal tradingMoney;
    //消费类型
    @Schema(description = " 消费类型 ")
    private String tradingType;
    //开票情况
    @Schema(description = " 开票情况 ")
    private String tradingInvoiceStatus;
    //停用系统来源描述
    @Schema(description = " 停用系统来源描述 ")
    private String deleteSource;
}
