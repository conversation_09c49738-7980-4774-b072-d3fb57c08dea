package com.v246.pc.tabledata.rpacustomer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ConsumeTransLineDTO {
    private static final long serialVersionUID = 1L;
    @Schema(description = " 出入口信息 ")
    private String beginProvince;
    @Schema(description = " 出入口信息 ")
    private String enProvince;
    @Schema(description = " 出入口信息 ")
    private String beginSite;
    @Schema(description = " 出入口信息 ")
    private String endSite;

    @Schema(description = " 金额 ")
    private String transPrice;

    @Schema(description = " 交易时间 ")
    private String transDate;

    @Schema(description = " 消费类型 ")
    private String transType;

    @Schema(description = " 开票情况 ")
    private String transStatus;
}
