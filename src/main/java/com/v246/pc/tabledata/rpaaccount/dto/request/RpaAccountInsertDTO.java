package com.v246.pc.tabledata.rpaaccount.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigInteger;
import java.math.BigDecimal;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> generate engine
 * @create 2021-01-13 10:33:28
 **/
@Data
public class RpaAccountInsertDTO {
    private static final long serialVersionUID = 1L;
    //账号
    @Schema(description = " 账号 ")
    private String accountName;
    //密码
    @Schema(description = " 密码 ")
    private String accountPassword;
}
