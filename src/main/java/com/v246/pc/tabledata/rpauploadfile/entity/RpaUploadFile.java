package com.v246.pc.tabledata.rpauploadfile.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.v246.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigInteger;
import java.math.BigDecimal;
import com.v246.pc.common.entity.DefaultEntity;
import com.baomidou.mybatisplus.annotation.KeySequence;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> generate engine
 * @since 2021-01-13 02:13:54
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(name="", description="")
public class RpaUploadFile extends DefaultEntity {

    private static final long serialVersionUID = 1L;
     //主键
     @Schema(description = " 主键 ")
             @TableId(value = "pk",type = IdType.AUTO)
     @TableField("pk")
    private Long pk;
     //MongoDB文件id
     @Schema(description = " MongoDB文件id ")
     @TableField("file_pk")
    private String filePk;
     //各个表主键
     @Schema(description = " 各个表主键 ")
     @TableField("table_pk")
    private String tablePk;
     //各个表的标识 （附件表：table_flag）
     @Schema(description = " 各个表的标识 （附件表：table_flag） ")
     @TableField("table_flag")
    private String tableFlag;
     //类别标识
     @Schema(description = " 类别标识 ")
     @TableField("item_type")
    private String itemType;
     //文件名称
     @Schema(description = " 文件名称 ")
     @TableField("file_name")
    private String fileName;
     //文件路径 文件存储地址+存储文件名
     @Schema(description = " 文件路径 文件存储地址+存储文件名 ")
     @TableField("file_path")
    private String filePath;
     //文件后缀名
     @Schema(description = " 文件后缀名 ")
     @TableField("file_exam")
    private String fileExam;
     //上传人主键
     @Schema(description = " 上传人主键 ")
     @TableField("upload_user")
    private Long uploadUser;
     //上传人姓名
     @Schema(description = " 上传人姓名 ")
     @TableField("upload_user_name")
    private String uploadUserName;
     //文件上传时间
     @Schema(description = " 文件上传时间 ")
     @TableField("upload_date")
    private Timestamp uploadDate;
     //停用系统来源描述
     @Schema(description = " 停用系统来源描述 ")
     @TableField("delete_source")
    private String deleteSource;



}
