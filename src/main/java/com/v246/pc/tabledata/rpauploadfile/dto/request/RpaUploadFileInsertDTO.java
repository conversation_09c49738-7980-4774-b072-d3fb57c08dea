package com.v246.pc.tabledata.rpauploadfile.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigInteger;
import java.math.BigDecimal;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> generate engine
 * @create 2021-01-13 02:13:54
 **/
@Data
public class RpaUploadFileInsertDTO {
    private static final long serialVersionUID = 1L;
    //MongoDB文件id
    @Schema(description = " MongoDB文件id ")
    private String filePk;
    //各个表主键
    @Schema(description = " 各个表主键 ")
    private String tablePk;
    //各个表的标识 （附件表：table_flag）
    @Schema(description = " 各个表的标识 （附件表：table_flag） ")
    private String tableFlag;
    //类别标识
    @Schema(description = " 类别标识 ")
    private String itemType;
    //文件名称
    @Schema(description = " 文件名称 ")
    private String fileName;
    //文件路径 文件存储地址+存储文件名
    @Schema(description = " 文件路径 文件存储地址+存储文件名 ")
    private String filePath;
    //文件后缀名
    @Schema(description = " 文件后缀名 ")
    private String fileExam;
    //上传人主键
    @Schema(description = " 上传人主键 ")
    private Long uploadUser;
    //上传人姓名
    @Schema(description = " 上传人姓名 ")
    private String uploadUserName;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //文件上传时间
    @Schema(description = " 文件上传时间 ")
    private Timestamp uploadDate;
    //停用系统来源描述
    @Schema(description = " 停用系统来源描述 ")
    private String deleteSource;
}
