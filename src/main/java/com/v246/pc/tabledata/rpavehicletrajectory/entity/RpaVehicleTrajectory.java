package com.v246.pc.tabledata.rpavehicletrajectory.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.v246.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigInteger;
import java.math.BigDecimal;
import com.v246.pc.common.entity.DefaultEntity;
import com.baomidou.mybatisplus.annotation.KeySequence;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> generate engine
 * @since 2021-01-27 03:01:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(name="", description="")
public class RpaVehicleTrajectory extends DefaultEntity {

    private static final long serialVersionUID = 1L;
     //主键
     @Schema(description = " 主键 ")
             @TableId(value = "pk",type = IdType.AUTO)
     @TableField("pk")
    private Long pk;
     //车牌号
     @Schema(description = " 车牌号 ")
     @TableField("vehicle_name")
    private String vehicleName;
     //账号
     @Schema(description = " 账号 ")
     @TableField("account_name")
    private String accountName;
     //密码
     @Schema(description = " 密码 ")
     @TableField("account_password")
    private String accountPassword;
     //发票代码
     @Schema(description = " 发票代码 ")
     @TableField("invoice_code")
    private String invoiceCode;
     //发票号码
     @Schema(description = " 发票号码 ")
     @TableField("invoice_no")
    private String invoiceNo;
     //通行日期起
     @Schema(description = " 通行日期起 ")
     @TableField("begin_date")
    private Timestamp beginDate;
     //通行日期止
     @Schema(description = " 通行日期止 ")
     @TableField("end_date")
    private Timestamp endDate;
     //出口
     @Schema(description = " 出口 ")
     @TableField("exitus")
    private String exitus;
     //入口
     @Schema(description = " 入口 ")
     @TableField("entrance")
    private String entrance;
     //交易金额
     @Schema(description = " 交易金额 ")
     @TableField("trading_money")
    private BigDecimal tradingMoney;
     //拆分金额
     @Schema(description = " 拆分金额 ")
     @TableField("single_money")
    private BigDecimal singleMoney;
     //票据序号
     @Schema(description = " 票据序号 ")
     @TableField("bill_no")
    private String billNo;
     //金额
     @Schema(description = " 金额 ")
     @TableField("invoice_money")
    private String invoiceMoney;
     //税率
     @Schema(description = " 税率 ")
     @TableField("invoice_rate")
    private String invoiceRate;
     //税额
     @Schema(description = " 税额 ")
     @TableField("invoice_tax")
    private String invoiceTax;
     //行程大序号
     @Schema(description = " 行程大序号 ")
     @TableField("route_no")
    private String routeNo;
     //停用系统来源描述
     @Schema(description = " 停用系统来源描述 ")
     @TableField("delete_source")
    private String deleteSource;
     //出口省简称
     @Schema(description = " 出口省简称 ")
     @TableField("exit_province_attr_name")
    private String exitProvinceAttrName;
     //入口省简称
     @Schema(description = " 入口省简称 ")
     @TableField("entrance_province_attr_name")
    private String entranceProvinceAttrName;

     //发票号码前两位
     @Schema(description = " 发票号码前两位 ")
     @TableField("invoice_first_no")
    private String invoiceFirstNo;
     //发票号码后两位
     @Schema(description = " 发票号码后两位 ")
     @TableField("invoice_end_no")
    private String invoiceEndNo;

     @Schema(description = " 汇总单号 ")
     @TableField("sum_no")
    private String sumNo;



}
