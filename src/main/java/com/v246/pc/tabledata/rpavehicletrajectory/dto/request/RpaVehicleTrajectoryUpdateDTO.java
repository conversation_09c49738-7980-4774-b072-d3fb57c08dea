package com.v246.pc.tabledata.rpavehicletrajectory.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigInteger;
import java.math.BigDecimal;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import javax.validation.constraints.NotBlank;
/**
 * <AUTHOR> generate engine
 * @create 2021-01-27 03:01:31
 **/
@Data
public class RpaVehicleTrajectoryUpdateDTO {
    private static final long serialVersionUID = 1L;
    private Long pk;
    //车牌号
    @Schema(description = " 车牌号 ")
    private String vehicleName;
    //账号
    @Schema(description = " 账号 ")
    private String accountName;
    //密码
    @Schema(description = " 密码 ")
    private String accountPassword;
    //发票代码
    @Schema(description = " 发票代码 ")
    private String invoiceCode;
    //发票号码
    @Schema(description = " 发票号码 ")
    private String invoiceNo;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //通行日期起
    @Schema(description = " 通行日期起 ")
    private Timestamp beginDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //通行日期止
    @Schema(description = " 通行日期止 ")
    private Timestamp endDate;
    //出口
    @Schema(description = " 出口 ")
    private String exitus;
    //入口
    @Schema(description = " 入口 ")
    private String entrance;
    //交易金额
    @Schema(description = " 交易金额 ")
    private BigDecimal tradingMoney;
    //拆分金额
    @Schema(description = " 拆分金额 ")
    private BigDecimal singleMoney;
    //票据序号
    @Schema(description = " 票据序号 ")
    private String billNo;
    //金额
    @Schema(description = " 金额 ")
    private String invoiceMoney;
    //税率
    @Schema(description = " 税率 ")
    private String invoiceRate;
    //税额
    @Schema(description = " 税额 ")
    private String invoiceTax;
    //行程大序号
    @Schema(description = " 行程大序号 ")
    private String routeNo;
    //停用系统来源描述
    @Schema(description = " 停用系统来源描述 ")
    private String deleteSource;
}
