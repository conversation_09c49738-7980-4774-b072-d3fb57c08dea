package com.v246.pc.tabledata.rpavehicletrajectory.controller;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.v246.common.config.annotation.SkipInterceptor;
import com.v246.common.kit.MyBatisKit;
import com.v246.spring.base.core.Result;
import com.v246.spring.base.core.database.Record;
import com.v246.pc.tabledata.rpavehicletrajectory.service.IRpaVehicleTrajectoryService;
import com.v246.pc.tabledata.rpavehicletrajectory.dto.request.RpaVehicleTrajectoryInsertDTO;
import com.v246.pc.tabledata.rpavehicletrajectory.dto.request.RpaVehicleTrajectoryUpdateDTO;
import com.v246.pc.tabledata.rpavehicletrajectory.dto.response.RpaVehicleTrajectoryListDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.v246.base.BaseController;
import java.math.BigInteger;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> generate engine
 * @since 2021-01-27 03:01:31
 */
@Tag(name = "", description = "")
@RestController
@RequestMapping("/rpaVehicleTrajectory")
public class RpaVehicleTrajectoryController extends BaseController {
    @Autowired
    IRpaVehicleTrajectoryService rpaVehicleTrajectoryService;
    @Autowired
    MyBatisKit myBatisKit;

    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping(value = "/queryPage")
    public Result queryPage(
            @Parameter(description = "{\"filters\": [],\"paginate\":{\"pageNumber\": 1,\"pageSize\": 50}}")
            @RequestBody JSONObject json
    ) {
        try {
            Page<Record> record = rpaVehicleTrajectoryService.queryPage(myBatisKit.getPageByFilterJson(json));
            return Result.success(record);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }
    @Operation(summary = "列表", description = "列表")
    @PostMapping(value = "/queryList")
    public Result queryList(
    ) {
        try {
            List<RpaVehicleTrajectoryListDTO> list = rpaVehicleTrajectoryService.queryList();
            return Result.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }

    @Operation(summary = "新增", description = "新增")
    @PostMapping(value = "/add")
    public Result add(@RequestBody RpaVehicleTrajectoryInsertDTO insertDTO) {
    rpaVehicleTrajectoryService.save(insertDTO);
        return Result.success();
    }

    @Operation(summary = "修改", description = "修改")
    @PostMapping(value = "/update")
    public Result update(@RequestBody RpaVehicleTrajectoryUpdateDTO updateDTO) {
    rpaVehicleTrajectoryService.update(updateDTO);
        return Result.success();
    }

    @Operation(summary = "物理删除", description = "物理删除")
    @PostMapping(value = "/delete")
    public Result delete(Long pk) {
        rpaVehicleTrajectoryService.delete(pk);
        return Result.success();
    }

    @Operation(summary = "逻辑删除", description = "逻辑删除")
    @PostMapping(value = "/deleteLogic")
    public Result deleteLogic(Long pk) {
        rpaVehicleTrajectoryService.deleteLogic(pk);
        return Result.success();
    }

    @Operation(summary = "物理批量删除", description = "物理批量删除")
    @PostMapping(value = "/deleteByIds")
    public Result deleteByIds(@RequestBody Long[] idList) {
        rpaVehicleTrajectoryService.deleteByIds(Arrays.asList(idList));
        return Result.success();
    }

    @Operation(summary = "逻辑批量删除", description = "逻辑批量删除")
    @PostMapping(value = "/deleteLogicByIds")
    public Result deleteLogicByIds(@RequestBody Long[] idList) {
        rpaVehicleTrajectoryService.deleteLogicByIds(Arrays.asList(idList));
        return Result.success();
    }

    @Operation(summary = "批量更新", description = "批量更新")
    @PostMapping(value = "/updateByList")
    public Result updateByList(@RequestBody RpaVehicleTrajectoryUpdateDTO[] rpaVehicleTrajectoryUpdateDTOList) {
        rpaVehicleTrajectoryService.updateByList(Arrays.asList(rpaVehicleTrajectoryUpdateDTOList));
        return Result.success();
    }


    @Operation(summary = "按照其他字段更新", description = "此方法仅供参考，不能直接使用")
    @PostMapping(value = "/updateByOther")
    public Result updateByOther(@RequestBody RpaVehicleTrajectoryUpdateDTO updateDTO) {
        rpaVehicleTrajectoryService.updateByOther(updateDTO);
        return Result.success();
    }

    @Operation(summary = "按照其他字段删除", description = "此方法仅供参考，不能直接使用")
    @PostMapping(value = "/deleteByOther")
    public Result deleteByOther(@RequestBody RpaVehicleTrajectoryUpdateDTO updateDTO) {
        rpaVehicleTrajectoryService.deleteByOther(updateDTO);
        return Result.success();
    }

}
