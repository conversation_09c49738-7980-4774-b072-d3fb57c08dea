package com.v246.pc.base;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.v246.base.BaseController;
import com.v246.common.kit.MyBatisKit;
import com.v246.pc.tabledata.rpaaccount.dto.request.RpaAccountInsertDTO;
import com.v246.pc.tabledata.rpaaccount.dto.request.RpaAccountUpdateDTO;
import com.v246.pc.tabledata.rpaaccount.service.IRpaAccountService;
import com.v246.pc.tabledata.rpavehiclerecord.dto.request.RpaVehicleRecordInsertDTO;
import com.v246.pc.tabledata.rpavehiclerecord.service.IRpaVehicleRecordService;
import com.v246.spring.base.core.Result;
import com.v246.spring.base.core.database.Record;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@Tag(name = "账号管理", description = "账号管理")
@RestController
@RequestMapping("/account")
public class AccountController extends BaseController {


    @Autowired
    IRpaAccountService rpaAccountService;
    @Autowired
    MyBatisKit myBatisKit;
    @Autowired
    IRpaVehicleRecordService rpaVehicleRecordService;

    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping(value = "/queryPage")
    public Result queryPage(
            @Parameter(description = "{\"filters\": [],\"paginate\":{\"pageNumber\": 1,\"pageSize\": 50}}")
            @RequestBody JSONObject json
    ) {
        try {
            Page<Record> record = rpaAccountService.queryPage(myBatisKit.getPageByFilterJson(json));
            return Result.success(record);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }


    @Operation(summary = "新增", description = "新增")
    @PostMapping(value = "/add")
    public Result add(@RequestBody RpaAccountInsertDTO insertDTO) {
        return rpaAccountService.save(insertDTO);
    }

    @Operation(summary = "修改", description = "修改")
    @PostMapping(value = "/update")
    public Result update(@RequestBody RpaAccountUpdateDTO updateDTO) {
        return rpaAccountService.update(updateDTO);
    }


    @Operation(summary = "删除", description = "删除")
    @PostMapping(value = "/deleteLogicByIds")
    public Result deleteLogicByIds(@RequestBody Long[] idList) {
        rpaAccountService.deleteLogicByIds(Arrays.asList(idList));
        return Result.success();
    }


    @Operation(summary = "下载车辆信息")
    @PostMapping("/downLoadVehicle")
    public Result downLoadVehicle(
            @RequestBody List<RpaVehicleRecordInsertDTO> rpaVehicleRecordInsertDTOList
    ) {
        try {
            return rpaVehicleRecordService.downLoadVehicle(rpaVehicleRecordInsertDTOList);
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("获取信息失败！");
        }
    }




}
