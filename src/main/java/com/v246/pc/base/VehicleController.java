package com.v246.pc.base;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.v246.base.BaseController;
import com.v246.common.kit.MyBatisKit;
import com.v246.pc.tabledata.rpastubrecord.dto.request.RpaStubRecordAddDTO;
import com.v246.pc.tabledata.rpatransactionrecord.dto.request.RpaTransactionRecordAddDTO;
import com.v246.pc.tabledata.rpavehicle.dto.request.RpaVehicleUpdateDTO;
import com.v246.pc.tabledata.rpavehicle.service.IRpaVehicleService;
import com.v246.spring.base.core.Result;
import com.v246.spring.base.core.database.Record;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "车辆管理", description = "车辆管理")
@RestController
@RequestMapping("/vehicle")
public class VehicleController extends BaseController {

    @Autowired
    IRpaVehicleService rpaVehicleService;
    @Autowired
    MyBatisKit myBatisKit;

    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping(value = "/queryPage")
    public Result queryPage(
            @Parameter(description = "{\"filters\": [],\"paginate\":{\"pageNumber\": 1,\"pageSize\": 50}}")
            @RequestBody JSONObject json
    ) {
        try {
            Page<Record> record = rpaVehicleService.queryPage(myBatisKit.getPageByFilterJson(json));
            return Result.success(record);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }


    @Operation(summary = "维护司机信息", description = "维护司机信息")
    @PostMapping(value = "/updateDriverInformation")
    public Result updateDriverInformation(@RequestBody RpaVehicleUpdateDTO updateDTO) {
        return rpaVehicleService.updateDriverInformation(updateDTO);
    }

    @Operation(summary = "下载发票明细", description = "下载发票明细")
    @PostMapping(value = "/downLoadInvoiceDetail")
    public Result downLoadInvoiceDetail(@RequestBody RpaStubRecordAddDTO rpaStubRecordAddDTO) {
        try {
            return rpaVehicleService.downLoadInvoiceDetail(rpaStubRecordAddDTO);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }


    @Operation(summary = "下载票据明细", description = "下载票据明细")
    @PostMapping(value = "/downTransactionDetail")
    public Result downTransactionDetail(@RequestBody RpaTransactionRecordAddDTO rpaTransactionRecordAddDTO) {
        try {
            return rpaVehicleService.downTransactionDetail(rpaTransactionRecordAddDTO);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }




}
