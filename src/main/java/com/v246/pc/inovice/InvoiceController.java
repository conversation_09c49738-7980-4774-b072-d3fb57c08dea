package com.v246.pc.inovice;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.v246.base.BaseController;
import com.v246.common.kit.MyBatisKit;
import com.v246.pc.tabledata.rpainvoice.dto.request.RpaExportInvoiceDTO;
import com.v246.pc.tabledata.rpainvoice.dto.request.RpaInvoiceExportDTO;
import com.v246.pc.tabledata.rpainvoice.service.IRpaInvoiceService;
import com.v246.pc.tabledata.rpauploadfile.dto.response.RpaUploadFileListDTO;
import com.v246.pc.tabledata.rpauploadfile.service.IRpaUploadFileService;
import com.v246.spring.base.core.Result;
import com.v246.spring.base.core.database.Record;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Tag(name = "发票管理", description = "发票管理")
@RestController
@RequestMapping("/invoice")
public class InvoiceController extends BaseController {


    @Autowired
    IRpaInvoiceService rpaInvoiceService;
    @Autowired
    MyBatisKit myBatisKit;
    @Autowired
    IRpaUploadFileService rpaUploadFileService;

    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping(value = "/queryPage")
    public Result queryPage(
            @Parameter(description = "{\"filters\": [],\"paginate\":{\"pageNumber\": 1,\"pageSize\": 50}}")
            @RequestBody JSONObject json
    ) {
        try {
            Page<Record> record = rpaInvoiceService.queryPage(myBatisKit.getPageByFilterJson(json));
            return Result.success(record);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }

    @Operation(summary = "查看详情-发票", description = "查看详情-发票")
    @PostMapping(value = "/getInvoice")
    public Result getInvoice(
            @RequestParam Long pk
    ) {
        try {
            List<RpaUploadFileListDTO> list = rpaUploadFileService.getInvoice(pk);
            return Result.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }

    @Operation(summary = "导出发票明细")
    @PostMapping("/export")
    public Result export(
            @RequestBody RpaInvoiceExportDTO rpaInvoiceExportDTO
    ) {
        try {
            String fileId = rpaInvoiceService.export(rpaInvoiceExportDTO);
            Map map = new HashMap<>();
            map.put("fileId",fileId);
            return Result.success(map);
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("获取信息失败！");
        }
    }


    @Operation(summary = "导出发票")
    @PostMapping("/exportInvoice")
    public Result exportInvoice(
            @RequestBody RpaExportInvoiceDTO rpaExportInvoiceDTO
    ) {
        try {
            return rpaInvoiceService.exportInvoice(rpaExportInvoiceDTO);
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("获取信息失败！");
        }
    }




}
