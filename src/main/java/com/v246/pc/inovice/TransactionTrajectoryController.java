package com.v246.pc.inovice;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.v246.base.BaseController;
import com.v246.common.kit.MyBatisKit;
import com.v246.pc.tabledata.rpatransactiontrajectory.service.IRpaTransactionTrajectoryService;
import com.v246.spring.base.core.Result;
import com.v246.spring.base.core.database.Record;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "票据信息", description = "票据信息")
@RestController
@RequestMapping("/transactionTrajectory")
public class TransactionTrajectoryController extends BaseController {

        @Autowired
        IRpaTransactionTrajectoryService rpaTransactionTrajectoryService;
        @Autowired
        MyBatisKit myBatisKit;

        @Operation(summary = "分页查询", description = "分页查询")
        @PostMapping(value = "/queryPage")
        public Result queryPage(
                @Parameter(description = "{\"filters\": [],\"paginate\":{\"pageNumber\": 1,\"pageSize\": 50}}")
                @RequestBody JSONObject json
        ) {
            try {
                Page<Record> record = rpaTransactionTrajectoryService.queryPage(myBatisKit.getPageByFilterJson(json));
                return Result.success(record);
            } catch (Exception e) {
                e.printStackTrace();
                return Result.fail(e.getMessage());
            }
        }
}
