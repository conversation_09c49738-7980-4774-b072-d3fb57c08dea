apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: prod
  name: aqucy-project-prod
  labels:
    app: aqucy-project-prod
spec:
  replicas: 1
  template:
    metadata:
      name: aqucy-project-prod
      labels:
        app: aqucy-project-prod
    spec:
      imagePullSecrets:
        - name: docker-secret
        - name: docker-secret-wl
      initContainers:
        - name: aqucy-project-init-prod
          image: nexus3.kube-ops.svc.cluster.local:8082/aqucy-init-image
          volumeMounts:
            - mountPath: /etc/localtime
              name: local-time-config
              readOnly: true
            - mountPath: aqucy-webapps-dir
              name: webapps-dir
      containers:
        - name: aqucy-project-prod
          image: nexus3.kube-ops.svc.cluster.local:8082/tomcat:8.5.49
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8080
              name: web
              protocol: TCP
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 500m
              memory: 512Mi
          livenessProbe:
            httpGet:
              path: /aqucy-project/health
              port: 8080
            initialDelaySeconds: 60
            timeoutSeconds: 5
            failureThreshold: 12
          readinessProbe:
            httpGet:
              path: /aqucy-project/health
              port: 8080
            initialDelaySeconds: 60
            timeoutSeconds: 5
            failureThreshold: 12
          volumeMounts:
            - mountPath: /etc/localtime
              name: local-time-config
              readOnly: true
            - mountPath: /usr/local/tomcat/webapps
              name: webapps-dir
      restartPolicy: Always
      volumes:
        - name: local-time-config
          hostPath:
            path: /etc/localtime
        - name: webapps-dir
          hostPath:
            path: aqucy-webapps-dir
  selector:
    matchLabels:
      app: aqucy-project-prod
---
apiVersion: v1
kind: Service
metadata:
  name: aqucy-project-service-prod
  namespace: prod
spec:
  selector:
    app: aqucy-project-prod
  ports:
    - port: 8080
#  type: NodePort
---
apiVersion: networking.k8s.io/v1beta1
kind: Ingress
metadata:
  namespace: prod
  name: aqucy-project-prod
  annotations:
    kubernetes.io/ingress.class: "prod"
spec:
  rules:
    - host: www.demo.com
      http:
        paths:
          - path: /aqucy-project
            backend:
              serviceName: aqucy-project-service-prod
              servicePort: 8080
