# 2.0框架使用文档
##  一、快速开始
### 1、修改项目名称
修改`artifactId`为你项目的英文名称
![avatar](images/1.png)

### 2、确认项目都启用哪一端
本框架目前支持PC端、 小程序端、企业微信端。启用方法是在`com.v246.MyApplication`以注解的方式启用。比如要启用PC端支持，需要添加@`EnablePC`注解，以下为各端的开启方法：

- `@EnablePC` 启用PC端
- `@EnableWxMiniProgram` 启用小程序端
- `@EnableWxCp` 启用企业微信端
![avatar](./images/2.png)
### 3、`RBAC`配置
本框架，约定采用`RBAC`权限控制，如果非`RBAC`，需自行适配。请打开`application.yml`主配置文件，找到`custom.rbac`配置点

- #### `authenticationQuery` 配置用户认证`SQL`（用户登录认证），请注意以下几点：
  1. 只能返回一个字段，即密码字段 
  2. `SQL`的`where`条件，最多只能有两个，一个是必须有的：“用户名”字段，另一个是可选的“域”字段
  3. 框架会自动分辨是否需要传入“域”字段的值，比如，只有一个条件(1个?号),框架会只传入用户名，如果有两个条件(2个？号)，则会将“用户名”和“密码”字段一起传入。
4、如果`SQL`有两个条件，但前端只传了一个“用户名”那么，框架会报错。反之则不会。
  
- #### `userRolesQuery` 配置抓取指定用户所担任的角色的`SQL`，需要注意以下几点
  1. 框架只会传入一个值，即当前登录人的用户名，所以，你只能用用户名为条件去抓取用户角色。
  2. 只需返回一个字段，即角色代码，返回的字段多了框架会报错。
  
- #### `permissionsQuery` 配置抓取指定用户所有权限的`SQL`，注意事项请参考`userRolesQuery`
### 4、定义你的密码匹配规则
打开`com.v246.MytPasswordEncoder`类，该楼共有两个方法需要你重写。
-  encode方法，用来加密，你可以使用我自己的加密方法进行加密，如果使用super，即为使用默认的：`BCryptPasswordEncoder`加密类
-  matches方法，密码校验方法，该方法会有两个参数：`rawPassword`和`encodedPassword`，其中`encodedPassword`为你数据库里的已加密完成的密码，`rawPassword`为前端传进来名文密码。
```java
//这是参考使用类，实际使用请不要无脑复制。
public class MytPasswordEncoder extends DefaultPasswordEncoder{
    @Override
    //加密方法
    public String encode(CharSequence rawPassword) {
        return PasswordKit.encode(rawPassword.toString().trim());
    }

    @Override
    //验证方法，主要是验证传进来的两个密码是否一至
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        log.debug("password1:{}   password2:{}",rawPassword,encodedPassword);
        return encode(rawPassword).equals(encodedPassword);
    }
}
```

### 5、其它配置

每端，都会有一些自定义的配置。默认每端的`JWT token` 签名都是一个，当然，你也可以自行修改。

- #### 小程序端

  配置位于：`custom.wxcp`

  登录地址：`/wxmini/login`

  绑定系统用户地址：`/wxmini/bindUser`
  
  小程序用户每次登录，都会记录在`wx_mini_user`表内，该表也记录了小程序用户与系统用户的绑定关系，表结构如下：

  ```mysql
  -- MYSQL DDL
  create table wx_mini_user
  (
      GID             bigint(255) auto_increment primary key,
      OPEN_ID         varchar(255)            null,
      UNION_ID        varchar(255)            null,
      CREATE_DATE     date                    null,
      CREATE_IP       varchar(255)            null,
      LAST_LOGIN_DATE date                    null,
      LAST_LOGIN_IP   varchar(255)            null,
      IS_DELETE       tinyint      default 0  null,
      DEALERS_CODE    varchar(255)            null,
      TYPE            varchar(255)            null,
      PHONE           bigint(255)             null,
      USER_ID         bigint       default 0  not null,
      user_name       varchar(255) default '' not null,
      domain          varchar(50)  default '' not null
  );
  ```
  
  注意这其中的：user_name字段，这即是系统用户名，如果该字段有值 ，框架即会认为，该小程序用户，已经绑定了系统用户。

- #### 企业微信端

  配置位于：`custom.wxmini`
  
  登录地址为：`/wxcp/login`
  
  绑定系统用户地址为：`/wxcp/bindSysUser`
  
  企业微信用户每次登录，都会记录在`wx_cp_user`表内，该表也记录了企业微信用户与系统用户的绑定关系，表结构如下：
  
  ```mysql
  -- MYSQL DDL
  create table wx_cp_user
  (
      GID             bigint(255) auto_increment
          primary key,
      WX_CP_USER_ID   varchar(255)            null,
      WX_CP_DEVICE_ID varchar(255)            null,
      CREATE_DATE     date                    null,
      CREATE_IP       varchar(255)            null,
      LAST_LOGIN_DATE date                    null,
      LAST_LOGIN_IP   varchar(255)            null,
      IS_DELETE       tinyint      default 0  null,
      USER_ID         bigint       default 0  not null,
      user_name       varchar(255) default '' not null,
      domain          varchar(50)  default '' not null
  );
  ```
  
  注意这其中的：user_name字段，这即是系统用户名，如果该字段有值 ，框架即会认为，该企业微信用户，已经绑定了系统用户。

- 以上数据库为`MYSQL`，如果你使用的是`ORACLE`，请息行修改为ORACLE的语法。

### 6、小程序前端

小程序前端共有三个模块：

- `isBindSysUser` 这是小程序入口页面，小程序运行后，会首先进入该页面，在这里，会询问后端，当前小程序用户是否已经绑定了系统用户，如果已经帮定，则跳转至index否则跳转至login
- `index` 这是小程序的主页面，只有已经绑定了系统账号的用户，才有权限进入这个页面。
- `login` 这是小程序账号绑定系统账号的地方。

需要你做的，美化`isBindSysUser` 和 `login` 这两个页面，然后在`index`页面完成业务逻辑。

如果需要判断用户角色、权限等，请使用以下代码：

```javascript
getApp().hasRole("roleCode");///判断是否有指定角色
getApp().hasPermissions("permissionCode");//判断是否有指定权限
```

### 7、企业微信前端

企业微信前端使用`vue-cli 4.1` 制作而成，和小程序类似，也有三个页面。

- `src/views/Index.vue` 这是企业微信端入口页面，前端运行后，会首先进入该页面，在这里，会询问后端，当前企业微信用户是否已经绑定了系统用户，如果已经帮定，则跳转至`Main.vue`否则跳转至`Login.vue`
- `src/views/Main.vue` 这是企业微信端的主页面，只有已绑定了系统账号的用户，才有权限进入这个页面。
- `src/views/Login.vue` 这是企业微信账号绑定系统账号的页面。

需要你做的，美化`Index.vue` 和 `Login.vue` 这两个页面，然后在`Main`页面完成业务逻辑。

如果需要判断用户角色、权限等，请使用以下代码：

```javascript
import PerKit from "../kit/PerKit";
......
PerKit.hasRole("roleCode");//判断是否有指定角色
PerKit.hasPermissions("permissionCode");///判断是否有指定权限
```

### 8、现在你应该可以使用本框架完成一些基本的开发，如果有其它问题，可以惨看`Q/A`

## 二、`Q/A`

- ### 问：登录时，我想将一些参数放入token里，该怎么做？

答：根据环境不同，可以实现：`XXXDelegate`接口，重写`createTokenData`方法即可，接口详细介绍如下

```java
//PC端端
public interface iPCLoginDelegate {
    //登录完成后，返回数据前，会调用此方法，返回的Map中的所有数据，会被放进Result中一起返回给前端
    Map<String,Object> loginSuccess(@NotNull  String userName, @Null String domain,@NotNull String token,@NotNull String refresh_token,@NotNull String roles,@NotNull String permisstions) throws JsonResultException;
    //登录成功后，调用此方法，返回的Map中的所有数据，会被放进token中，以后可以使用TokenKit获取
    Map<String,String> createTokenData(@NotNull  String userName, @Null String domain) throws JsonResultException;
    //登录失败，会调用此方法，此接口为默认接口，子类中可以不用实现，但如果要在登录失败后做一些事情，则需要重写此方法。
    default void loginFailure(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, AuthenticationException e) {

    }
}

//小程序端端
public interface IWxMiniLoginDelegate {
    //登录成功后，调用此方法，返回的Map中的所有数据，会被放进token中，以后可以使用TokenKit获取
    Map<String,String> createTokenData(@NotNull String openid,String unionId);
    //登录完成后，返回数据前，会调用此方法，返回的Map中的所有数据，会被放进Result中一起返回给前端
    Map<String,Object> loginSuccess(@NotNull  String userName, @Null String domain, @NotNull String token, @NotNull String refresh_token, @NotNull String roles, @NotNull String permisstions) throws JsonResultException;
    //当小程序用户绑定系统用户成功后，会调用此方法，若有需要可以在这个方法中做一些其它业务必要操作
    void bindUserSuccess(Long wxMiniUserGid);
    //登录失败，会调用此方法，此接口为默认接口，子类中可以不用实现，但如果要在登录失败后做一些事情，则需要重写此方法。
    default void loginFailure(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, AuthenticationException e) {

    }
}

//企业微信端
public interface IWxCpLoginDelegate {
    //登录成功后，调用此方法，返回的Map中的所有数据，会被放进token中，以后可以使用TokenKit获取
    Map<String,String> createTokenData(@NotNull String userId);
    //如果你可以根据企业微信用户的用户信息自动绑定用户，那么可以实现此方法，比如，用户信自里的手机号、邮箱、扩展字段等，用这些是可以定位用户的。
    //返回值是系统的用户名
    String auitoBindSysUser(WxCpUser wxCpUser);
    //登录完成后，返回数据前，会调用此方法，返回的Map中的所有数据，会被放进Result中一起返回给前端
    Map<String,Object> loginSuccess(@NotNull String userId, @Null String domain, @NotNull String token, @NotNull String refresh_token, @NotNull String roles, @NotNull String permisstions) throws JsonResultException;
    //当小程序用户绑定系统用户成功后，会调用此方法，若有需要可以在这个方法中做一些其它业务必要操作
    void bindUserSuccess(Long wxMiniUserGid);
    //登录失败，会调用此方法，此接口为默认接口，子类中可以不用实现，但如果要在登录失败后做一些事情，则需要重写此方法。
    default void loginFailure(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, AuthenticationException e) {

    }
}
```

- ### 问：登录成功后，我有一些自定义数据想返回给前端用，请问该怎么做？

答：参看上个问题，根据环境不同，可以实现：`XXXDelegate`接口，重写`loginSuccess`方法即可

- ### 问：我想导出Excel，有没有简单一些的办法？

答：框架已经集成了通用的办法，直接将excel导出至`mongodb`里，并返回`fileid`,详细用法如下：

```java
@Test
    /**
     * 直接导出至mongoDb，返回fileid
     * */
    public void testExportMongo(){
        String fileName = "演示导出数据.xlsx";
        //headMap里存的是数据表的字段（英文）和excel的title(中文)的对应关系。
        LinkedHashMap<String,String> headerMap = new LinkedHashMap<>();
        headerMap.put("ID","主键");
        headerMap.put("USER_NAME","用户名");
        headerMap.put("name","姓名");
        headerMap.put("total_memory","累计工资");
        headerMap.put("is_valid","是否有效");
        //传进去的SQL里的字段，一定要与headerMap里的一一对应。
        MongoFile mongoFile = mongoKit.exportExcel("SELECT ID,USER_NAME,name,total_memory,is_valid FROM user", fileName, headerMap);
        String fileId = mongoFile.getPk();
        Assert.assertNotNull(mongoFile);
    }

```

- ### 问：前端封装的table能不能直接导出Excel？自己解析`filterJson`太麻烦了

答：没问题，使用方法如下

```java
@Test
    /**
     * 使用filterJson直接导出至mongoDb，返回fileid
     * */
    public void testExportMongoForFilterJson(){
        JSONArray jr = null;
        String fileName = "演示导出数据.xlsx";
        LinkedHashMap<String,String> headerMap = new LinkedHashMap<>();
        headerMap.put("ID","主键");
        headerMap.put("USER_NAME","用户名");
        headerMap.put("name","姓名");
        headerMap.put("total_memory","累计工资");
        headerMap.put("is_valid","是否有效");
        MongoFile mongoFile = mongoKit.exportExcel("SELECT ID,USER_NAME,name,total_memory,is_valid FROM zy_user", jr,fileName, headerMap);
        String fileId = mongoFile.getPk();
        Assert.assertNotNull(mongoFile);
    }
```

- ### 问：我需要将一些数据导入到系统内。

答：框架已经集成通用的excel导入方法，适用大部份数据导入场景，使用方法如下

```java
@Autowired
    ImportExcelKit importExcelKit;
    @Autowired
    Db db;
    @Test
public void testGetFirst() throws Exception{
        InputStream inputStream = new FileInputStream(new File("d:\\ftp\\import.xlsx"));
        LinkedList<ExcelColumnVO> list = new LinkedList<>();
        ExcelColumnVO excelColumnVO = new ExcelColumnVO();
    	//excel里的title名称
        excelColumnVO.setColumnName("基地");
    	//数据表里对应的字段
        excelColumnVO.setDbFieldName("base_jdi")
        //字符串验证
        excelColumnVO.setCv(new StringValidation());
        list.add(excelColumnVO);

        excelColumnVO = new ExcelColumnVO();
        excelColumnVO.setColumnName("到站/到港");
        excelColumnVO.setDbFieldName("dddddddddddd");
        
        excelColumnVO = new ExcelColumnVO();
        excelColumnVO.setColumnName("到达时间");
        /**这里需要注意，如果指定了字段类型，导入程序，就会将将excel的值 转换成你指定的类型*/
        /**如果没有指定类型，一率按照字符串处理*/
        excelColumnVO.setDataType(ExcelColumnVO.DataType.DATE);
        excelColumnVO.setDbFieldName("dddddddddddd");


        //整数验证
        IntegerValidation integerValidation = new IntegerValidation();
        integerValidation.setMaxValue(50);
        integerValidation.setMinValue(10);
        //必须有值 ，否则导入程序会报错
        integerValidation.setRequired(true);
        integerValidation.setRequiredErrMsg("XXX字段必须有值");
        excelColumnVO.setCv(integerValidation);
        list.add(excelColumnVO);

        //参数1：excel列信息  参数2：要导入的excel输入流,第三个参数，是否为xlsx格式
        ImportResult importResult = importExcelKit.importExcel(list, inputStream, true);
        List<String> errList = importResult.getErrorList();
        //如果返回的list大于0，证名有错误
        if (errList.size() > 0) {
            //导入失败，失败原因都在errList里
            for (String str : errList) {
                System.out.println(str);
            }
        }else{
            //导入成功，Excel内的数据，会转换成Record返回，拿到数据后，就可以进行自己的业务了。
            List<Record> records = importResult.getResultList();
            //写入数据库
            db.save("表名",records);
            System.out.println("共计导入条数："+records.size());
            for (Record rc : records) {
//                System.out.println("货物名称:"+rc.get("货物名称")+"  采购数量:"+rc.get("采购数量"));
            }
        }

    }

```

导入时，如果需要对导入的数据进行验证，可以使用：`ColumnValidation`接口的实现类完成，当然，你也可以实现这个接口，自定义验证信息，目前框架内置的的验证一共有以下几种：

```
DateValidation :日期验证
FloatValidation ：浮点型验证
PatternValidation ：正则验证
LongValidation ：长整型验证
StringValidation ：字符串验证
IntegerValidation  ：整型验证
```

- ### 问：`RBAC`如何细粒度的控制权限，比如某些资源只能由指定角色访问，或利有权限代码控制资源的访问等。

答：框架集成了`springSecurity`,可以实现细料度的权限控制。用法也分为前端和后端两种。

#### 1、前端

##### （1）、小程序端

```javascript
//判断当前用户是否拥有指定角色
if(getApp().hasRole("ADMIN")){
    ......
}
//判断当前用户是否有指定权限
if(getApp().hasPermissions("CRATE_USER")){
    ......
}
```

##### （2）、企业微信端

```javascript
import PerKit from "../kit/PerKit";
//判断当前用户是否拥有指定角色
if(PerKit.hasRole("ADMIN")){
    ......
}
//判断当前用户是否有指定权限
if(PerKit.hasPermissions("CRATE_USER")){
    ......
}
```

#### 2、后端

```java
@RestController
@Tag(name = "RABC测试", description = "RABC测试")
public class SecurityController {
    //判断当前用户是否有指定权限
    @PostMapping("/sayHelloForPermission")
    @PreAuthorize(value = "hasPermission(null,'CREATE_USER')")
    @ApiOperation("需要验证权限(CREATE_USER)的sayHello")
    public Result sayHelloForPermission() {
        return Result.success("hello");
    }

    //判断当前用户是否拥有指定角色
    @PostMapping("/sayHelloForRole")
    @PreAuthorize(value = "hasRole('ADMIN')")
    @ApiOperation("需要验证角我(ADMIN)的sayHello")
    public Result sayHelloForRole() {
        return Result.success("hello");
    }
	//不需要验证权限或角色，但要求必须得登录过
    @PostMapping("/sayHelloNoAuth")
    @ApiOperation("不需要验证权限的sayHello")
    public Result sayHelloNoAuth() {
        return Result.success("hello");
    }
}
```

- ### 问：如何放开一个`RequestMapping`，不走登录验证拦截器？让这个`url`地址可以公开的随便的被访问？

答：只需要在目标Controller的方法上添加一个`@SkipInterceptor`注解即可。

```java
@SkipInterceptor
@PostMapping("/helloWorld")
@ApiOperation("helloWorld")
public Result sayHelloNoAuth() {
    return Result.success("helloWorld");
}
```

- ### 问：`Mybatis`的`Entity`、`xml`配置文件每个业务模块都要再建一次，能不能简化一下？

答：框架级别支持代码生成，会自动生成`controller、service、entity、mapper、xml`等文件。

```java
package com.v246;

public class CodeGeneratortester extends BaseCodeGenerator {
    @Override
    protected String getJdbcUrl() {
        //jdbc url
        return "***********************************************************************************";
    }

    @Override
    protected String getDriverClass() {
        //jdbc driver
        return "com.mysql.jdbc.Driver";
    }

    @Override
    protected String getUserName() {
        //数据库访问的用户名
        return "wms";
    }

    @Override
    protected String getPassword() {
        //数据库访问的密码
        return "D392_32Mlm_9=0";
    }

    @Override
    protected String getAuthor() {
        //代码的作者，填你名就行了
        return "sl";
    }

    @Override
    protected String getBasePackage() {
        //要把代码生成到哪个包里
        return "com.v246.pc.base";
    }

    @Override
    protected String getOutDir() {
        //这个不用动
        String projectPath = System.getProperty("user.dir");
        //这个也不用动
        return projectPath + "/src/main/java";
    }
	//主方法入口，执行这个方法，会询问你要生成哪张表，填一下就可以生成了
    public static void main(String[] args) {
        CodeGeneratortester codeGeneratortester = new CodeGeneratortester();
        codeGeneratortester.generator();
    }
}

```

- ### 问：如何写一个定时执行的TASK任务？

答：框架级别，直接支持，要写一个TASK任务，一般分为几步：

1、确认`com.v246.MyApplication`类上，是否启用了`@EnableScheduling`注释，基未启用，请启用

2、新建TASK类，名子请自定义，所在包就自定义

3、为该类打上`@Component`标签

4、在该类中新建一个没有返回值、没有参数的 的方法，方法名自定义

5、为这个方法添加注释：`@Scheduled(cron='......')`

6、`cron`表达式的写法请自行百度，这里为大家推荐一个在线`Cron`表大式生成器： http://qqe2.com/cron 

7、以下为完理的`TASK`定时实例

```java
package com.v246;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class TaskDemo {
    @Scheduled(cron = "0/1 * * * * ? ")
    public void task(){
        System.out.println("ddddd");

    }
}
```
- ### 问：如何查看TOMCAT的日志？
> 答：注意，本方法只适用于项目已部署至服务器上的情况。
- 以拄需要访问：swagger-ui.html进行API操作，现在请访问api.html
- 访问方式如：http://xxx.xx.com/youProject/api.html 
- 点击右上角的：ServerLog 按钮，即可实时查看服务器日志
- 弹出窗口没有遮罩，所以你可以一边执行API，一边查看服务器的日志输出
- 窗口可以拖动更改位置，可以拖动右下角更改大小，可以最大化
- 要注意，本方法无法查看项目最初启动时的日志。
- 项目的启动日志请用jenkins查看（构建成功后，会输出，在Console Output中可以看到）






































