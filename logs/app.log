2025-06-09 15:09:30 [main] INFO  c.a.n.client.logging.NacosLogging - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapterBuilder
2025-06-09 15:09:30 [main] INFO  c.a.n.client.logging.NacosLogging - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapterBuilder
2025-06-09 15:09:30 [main] INFO  c.a.n.client.logging.NacosLogging - Nacos Logging Adapter: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapter match ch.qos.logback.classic.Logger success.
2025-06-09 15:09:30 [main] INFO  c.a.n.client.logging.NacosLogging - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.log4j2.Log4j2NacosLoggingAdapterBuilder
2025-06-09 15:09:31 [main] INFO  com.Application - Starting Application using Java 17.0.12 with PID 65714 (/Users/<USER>/aquaqu/springBootProject/docs/rpa/trunk/target/classes started by aquaqu in /Users/<USER>/aquaqu/springBootProject/docs/rpa/trunk)
2025-06-09 15:09:31 [main] INFO  com.Application - The following 1 profile is active: "self"
2025-06-09 15:09:31 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.Application]
2025-06-09 15:09:31 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-09 15:09:31 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.Application]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:193)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:418)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.Application.main(Application.java:25)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'application' for bean class [com.v246.Application] conflicts with existing, non-compatible bean definition of same name and class [com.Application]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:345)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:280)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:203)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:172)
	... 13 common frames omitted
2025-06-09 15:09:31 [Thread-2] WARN  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-09 15:09:31 [Thread-2] WARN  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Destruction of the end
2025-06-09 15:10:20 [main] INFO  c.a.n.client.logging.NacosLogging - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapterBuilder
2025-06-09 15:10:20 [main] INFO  c.a.n.client.logging.NacosLogging - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapterBuilder
2025-06-09 15:10:20 [main] INFO  c.a.n.client.logging.NacosLogging - Nacos Logging Adapter: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapter match ch.qos.logback.classic.Logger success.
2025-06-09 15:10:20 [main] INFO  c.a.n.client.logging.NacosLogging - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.log4j2.Log4j2NacosLoggingAdapterBuilder
2025-06-09 15:10:20 [main] INFO  com.MyApplication - Starting MyApplication using Java 17.0.12 with PID 66351 (/Users/<USER>/aquaqu/springBootProject/docs/rpa/trunk/target/classes started by aquaqu in /Users/<USER>/aquaqu/springBootProject/docs/rpa/trunk)
2025-06-09 15:10:20 [main] INFO  com.MyApplication - The following 1 profile is active: "self"
2025-06-09 15:10:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 15:10:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 15:10:30 [Thread-2] WARN  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Start destroying ThreadPool
2025-06-09 15:10:30 [Thread-2] WARN  c.a.n.c.executor.ThreadPoolManager - [ThreadPoolManager] Destruction of the end
2025-06-10 15:07:00 [main] INFO  o.a.p.p.font.FileSystemFontProvider - 398 new font files found, font cache will be re-built
2025-06-10 15:07:00 [main] INFO  o.a.p.p.font.FileSystemFontProvider - Building on-disk font cache, this may take a while
2025-06-10 15:07:00 [main] WARN  o.a.p.p.font.FileSystemFontProvider - Could not load font file '/System/Library/Fonts/Supplemental/NISC18030.ttf': 'head' table is mandatory
2025-06-10 15:07:01 [main] INFO  o.a.p.p.font.FileSystemFontProvider - Finished building on-disk font cache, found 795 fonts
